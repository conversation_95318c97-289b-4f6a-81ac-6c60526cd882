'use client'

import {
  Package,
  Users,
  DollarSign,
  AlertTriangle,
  Activity,
  Clock,
  Plus,
  BarChart3,
  PieChart,
  Bell,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Target,
  ShoppingCart,
  CreditCard
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import type { DashboardStats } from '@/types'

interface DashboardStatsProps {
  stats: DashboardStats
  onSectionChange?: (section: string) => void
}

interface PerformanceMetric {
  label: string
  value: number
  previousValue: number
  change: number
  changeType: 'increase' | 'decrease' | 'neutral'
  icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>
  color: string
  bgColor: string
}

export default function DashboardStats({ stats, onSectionChange }: DashboardStatsProps) {
  const { resolvedTheme } = useTheme()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [animatedStats, setAnimatedStats] = useState(stats)

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Animate stats changes
  useEffect(() => {
    setAnimatedStats(stats)
  }, [stats])

  // Auto-refresh functionality
  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    setIsRefreshing(false)
  }

  // Performance metrics with trend analysis
  const performanceMetrics: PerformanceMetric[] = [
    {
      label: 'Products in List',
      value: animatedStats.totalProducts,
      previousValue: Math.max(0, animatedStats.totalProducts - Math.floor(Math.random() * 5)),
      change: 12.5,
      changeType: 'increase',
      icon: Package,
      color: '#3b82f6',
      bgColor: resolvedTheme === 'dark' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
    },
    {
      label: 'Customer Debts',
      value: animatedStats.totalDebts,
      previousValue: Math.max(0, animatedStats.totalDebts + Math.floor(Math.random() * 3)),
      change: -8.3,
      changeType: 'decrease',
      icon: Users,
      color: '#10b981',
      bgColor: resolvedTheme === 'dark' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.05)'
    },
    {
      label: 'Total Debt Amount',
      value: animatedStats.totalDebtAmount,
      previousValue: animatedStats.totalDebtAmount + Math.floor(Math.random() * 1000),
      change: -15.7,
      changeType: 'decrease',
      icon: DollarSign,
      color: '#f59e0b',
      bgColor: resolvedTheme === 'dark' ? 'rgba(245, 158, 11, 0.1)' : 'rgba(245, 158, 11, 0.05)'
    },
    {
      label: 'Low Stock Items',
      value: animatedStats.lowStockItems,
      previousValue: Math.max(0, animatedStats.lowStockItems + Math.floor(Math.random() * 2)),
      change: -25.0,
      changeType: 'decrease',
      icon: AlertTriangle,
      color: '#ef4444',
      bgColor: resolvedTheme === 'dark' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)'
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-PH', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // Quick action handlers
  const handleQuickAction = (action: string) => {
    if (onSectionChange) {
      switch (action) {
        case 'add-product':
          onSectionChange('products')
          break
        case 'record-debt':
          onSectionChange('debts')
          break
        case 'view-analytics':
          onSectionChange('api-graphing')
          break
        case 'manage-stock':
          onSectionChange('products')
          break
        case 'view-history':
          onSectionChange('history')
          break
        default:
          break
      }
    }
  }

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Dashboard Header with Real-time Info */}
      <div
        className="rounded-2xl shadow-lg p-6 border transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
          background: resolvedTheme === 'dark'
            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        <div className="flex items-center justify-between">
          <div>
            <h2
              className="text-2xl font-bold mb-2 flex items-center gap-3"
              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
            >
              <Activity className="h-7 w-7 text-green-500" />
              Dashboard Overview of your Revantad Store
            </h2>
            <p
              className="text-sm flex items-center gap-2"
              style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}
            >
              <Clock className="h-4 w-4" />
              Last updated: {formatTime(currentTime)} • Real-time monitoring active
            </p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.08)',
              border: resolvedTheme === 'dark' ? '1px solid rgba(34, 197, 94, 0.3)' : '1px solid rgba(34, 197, 94, 0.2)',
              color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'
            }}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Enhanced Performance Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {performanceMetrics.map((metric, index) => {
          const Icon = metric.icon
          const TrendIcon = metric.changeType === 'increase' ? ArrowUpRight : ArrowDownRight
          const isPositiveTrend = metric.changeType === 'increase'

          return (
            <div
              key={index}
              className="group rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] cursor-pointer border"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
                background: resolvedTheme === 'dark'
                  ? `linear-gradient(135deg, #1e293b 0%, ${metric.bgColor} 100%)`
                  : `linear-gradient(135deg, #ffffff 0%, ${metric.bgColor} 100%)`
              }}
            >
              <div className="flex items-start justify-between mb-4">
                <div
                  className="p-3 rounded-xl transition-all duration-300 group-hover:scale-110"
                  style={{
                    backgroundColor: metric.bgColor,
                    border: `1px solid ${metric.color}20`
                  }}
                >
                  <Icon
                    className="h-6 w-6 transition-all duration-300"
                    style={{ color: metric.color }}
                  />
                </div>
                <div className="flex items-center gap-1">
                  <TrendIcon
                    className={`h-4 w-4 ${isPositiveTrend ? 'text-green-500' : 'text-red-500'}`}
                  />
                  <span
                    className={`text-sm font-semibold ${isPositiveTrend ? 'text-green-500' : 'text-red-500'}`}
                  >
                    {Math.abs(metric.change)}%
                  </span>
                </div>
              </div>

              <div>
                <p
                  className="text-sm font-medium mb-2 transition-colors duration-300"
                  style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}
                >
                  {metric.label}
                </p>
                <p
                  className="text-3xl font-bold mb-1 transition-colors duration-300"
                  style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                >
                  {metric.label.includes('Amount') ? formatCurrency(metric.value) : metric.value.toLocaleString()}
                </p>
                <p
                  className="text-xs transition-colors duration-300"
                  style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af' }}
                >
                  vs {metric.label.includes('Amount') ? formatCurrency(metric.previousValue) : metric.previousValue.toLocaleString()} last period
                </p>
              </div>

              {/* Mini Progress Bar */}
              <div className="mt-4">
                <div
                  className="h-1 rounded-full overflow-hidden"
                  style={{ backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f1f5f9' }}
                >
                  <div
                    className="h-full rounded-full transition-all duration-1000 ease-out"
                    style={{
                      backgroundColor: metric.color,
                      width: `${Math.min(100, (metric.value / (metric.value + metric.previousValue)) * 100)}%`
                    }}
                  />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Enhanced Quick Actions Grid */}
      <div
        className="rounded-2xl shadow-lg p-6 border transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
          background: resolvedTheme === 'dark'
            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        <div className="flex items-center gap-3 mb-6">
          <Zap className="h-6 w-6 text-yellow-500" />
          <h3
            className="text-xl font-bold transition-colors duration-300"
            style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
          >
            Quick Actions
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Add Product Action */}
          <button
            onClick={() => handleQuickAction('add-product')}
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'
                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'
            }}
            title="Navigate to Products section to add new items"
          >
            <div className="p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110"
                 style={{ backgroundColor: 'rgba(59, 130, 246, 0.1)' }}>
              <Plus className="h-6 w-6 text-blue-600" />
            </div>
            <p className="font-semibold mb-1" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
              Add Product
            </p>
            <p className="text-xs text-center" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Add new item to inventory
            </p>
          </button>

          {/* Record Debt Action */}
          <button
            onClick={() => handleQuickAction('record-debt')}
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'
                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'
            }}
            title="Navigate to Debts section to record customer debt"
          >
            <div className="p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110"
                 style={{ backgroundColor: 'rgba(16, 185, 129, 0.1)' }}>
              <CreditCard className="h-6 w-6 text-green-600" />
            </div>
            <p className="font-semibold mb-1" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
              Record Debt
            </p>
            <p className="text-xs text-center" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Add customer debt record
            </p>
          </button>

          {/* View Analytics Action */}
          <button
            onClick={() => handleQuickAction('view-analytics')}
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'
                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'
            }}
            title="Navigate to API Graphing & Visuals for business analytics"
          >
            <div className="p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110"
                 style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)' }}>
              <BarChart3 className="h-6 w-6 text-yellow-600" />
            </div>
            <p className="font-semibold mb-1" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
              View Analytics
            </p>
            <p className="text-xs text-center" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Business insights & reports
            </p>
          </button>

          {/* Manage Inventory Action */}
          <button
            onClick={() => handleQuickAction('manage-stock')}
            className="group flex flex-col items-center p-6 rounded-xl transition-all duration-300 hover:scale-[1.05] hover:shadow-lg border focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
              background: resolvedTheme === 'dark'
                ? 'linear-gradient(135deg, #334155 0%, #475569 100%)'
                : 'linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%)'
            }}
            title="Navigate to Products section to manage inventory levels"
          >
            <div className="p-3 rounded-full mb-3 transition-all duration-300 group-hover:scale-110"
                 style={{ backgroundColor: 'rgba(139, 69, 19, 0.1)' }}>
              <ShoppingCart className="h-6 w-6 text-amber-700" />
            </div>
            <p className="font-semibold mb-1" style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}>
              Manage Stock
            </p>
            <p className="text-xs text-center" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Update inventory levels
            </p>
          </button>
        </div>
      </div>

      {/* Enhanced Store Overview & Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Store Overview */}
        <div
          className="rounded-2xl shadow-lg p-6 border transition-all duration-300"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
              : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
          }}
        >
          <div className="flex items-center gap-3 mb-6">
            <Target className="h-6 w-6 text-blue-500" />
            <h3
              className="text-xl font-bold transition-colors duration-300"
              style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
            >
              Store Overview
            </h3>
          </div>

          <div className="space-y-4">
            {[
              { label: 'Products in List', value: animatedStats.totalProducts, icon: Package, color: '#3b82f6' },
              { label: 'Outstanding Debts', value: animatedStats.totalDebts, icon: Users, color: '#10b981' },
              { label: 'Total Amount Owed', value: formatCurrency(animatedStats.totalDebtAmount), icon: DollarSign, color: '#f59e0b' },
              { label: 'Items Need Restocking', value: animatedStats.lowStockItems, icon: AlertTriangle, color: animatedStats.lowStockItems > 0 ? '#ef4444' : '#10b981' }
            ].map((item, index) => {
              const Icon = item.icon
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] border"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                    border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="p-2 rounded-lg"
                      style={{ backgroundColor: `${item.color}20` }}
                    >
                      <Icon className="h-5 w-5" style={{ color: item.color }} />
                    </div>
                    <span
                      className="font-medium transition-colors duration-300"
                      style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}
                    >
                      {item.label}
                    </span>
                  </div>
                  <span
                    className="text-lg font-bold transition-colors duration-300"
                    style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                  >
                    {typeof item.value === 'string' ? item.value : item.value.toLocaleString()}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Recent Activities */}
        <div
          className="rounded-2xl shadow-lg p-6 border transition-all duration-300"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
            border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
            background: resolvedTheme === 'dark'
              ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
              : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
          }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Bell className="h-6 w-6 text-green-500" />
              <h3
                className="text-xl font-bold transition-colors duration-300"
                style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
              >
                Recent Activities
              </h3>
            </div>
            <button
              onClick={() => handleQuickAction('view-history')}
              className="text-sm px-3 py-1 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.08)',
                color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'
              }}
              title="Navigate to History section for detailed activity logs"
            >
              View All
            </button>
          </div>

          <div className="space-y-3">
            {[
              {
                action: 'New product added',
                item: 'Coca Cola 1.5L',
                time: '2 minutes ago',
                type: 'product',
                icon: Package,
                priority: 'normal',
                clickable: true
              },
              {
                action: 'Debt payment received',
                item: 'Juan Dela Cruz - ₱500',
                time: '15 minutes ago',
                type: 'payment',
                icon: DollarSign,
                priority: 'high',
                clickable: true
              },
              {
                action: 'Low stock alert',
                item: 'Rice 25kg - Only 3 left',
                time: '1 hour ago',
                type: 'alert',
                icon: AlertTriangle,
                priority: 'urgent',
                clickable: true
              },
              {
                action: 'New customer debt',
                item: 'Maria Santos - ₱1,200',
                time: '2 hours ago',
                type: 'debt',
                icon: Users,
                priority: 'normal',
                clickable: true
              }
            ].map((activity, index) => {
              const Icon = activity.icon
              const getActivityColor = (type: string) => {
                switch (type) {
                  case 'product': return '#3b82f6'
                  case 'payment': return '#10b981'
                  case 'alert': return '#ef4444'
                  case 'debt': return '#f59e0b'
                  default: return '#6b7280'
                }
              }

              const getPriorityIndicator = (priority: string) => {
                switch (priority) {
                  case 'urgent': return { color: '#ef4444', pulse: true }
                  case 'high': return { color: '#f59e0b', pulse: false }
                  case 'normal': return { color: '#10b981', pulse: false }
                  default: return { color: '#6b7280', pulse: false }
                }
              }

              const priorityInfo = getPriorityIndicator(activity.priority)

              return (
                <button
                  key={index}
                  onClick={() => activity.clickable && handleQuickAction(activity.type === 'product' ? 'add-product' : activity.type === 'debt' ? 'record-debt' : 'view-history')}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-300 hover:scale-[1.01] border text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 ${activity.clickable ? 'cursor-pointer' : 'cursor-default'}`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb',
                    border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #e5e7eb'
                  }}
                  disabled={!activity.clickable}
                  title={activity.clickable ? `Click to navigate to ${activity.type} section` : ''}
                >
                  <div className="relative">
                    <div
                      className={`p-2 rounded-full flex-shrink-0 ${priorityInfo.pulse ? 'animate-pulse' : ''}`}
                      style={{ backgroundColor: `${getActivityColor(activity.type)}20` }}
                    >
                      <Icon className="h-4 w-4" style={{ color: getActivityColor(activity.type) }} />
                    </div>
                    {/* Priority indicator */}
                    <div
                      className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 ${priorityInfo.pulse ? 'animate-ping' : ''}`}
                      style={{
                        backgroundColor: priorityInfo.color,
                        borderColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
                      }}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p
                      className="text-sm font-medium truncate"
                      style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
                    >
                      {activity.action}
                    </p>
                    <p
                      className="text-xs truncate"
                      style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}
                    >
                      {activity.item}
                    </p>
                  </div>
                  <span
                    className="text-xs flex-shrink-0"
                    style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af' }}
                  >
                    {activity.time}
                  </span>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div
        className="rounded-2xl shadow-lg p-6 border transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb',
          background: resolvedTheme === 'dark'
            ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
            : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        <div className="flex items-center gap-3 mb-6">
          <PieChart className="h-6 w-6 text-purple-500" />
          <h3
            className="text-xl font-bold transition-colors duration-300"
            style={{ color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827' }}
          >
            Performance Summary
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-3">
              <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke={resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'}
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="2"
                  strokeDasharray="85, 100"
                  className="animate-pulse"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-green-500">85%</span>
              </div>
            </div>
            <p className="text-sm font-medium" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Store Health
            </p>
          </div>

          <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-3">
              <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke={resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'}
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2"
                  strokeDasharray="72, 100"
                  className="animate-pulse"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-blue-500">72%</span>
              </div>
            </div>
            <p className="text-sm font-medium" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Inventory Level
            </p>
          </div>

          <div className="text-center">
            <div className="relative w-20 h-20 mx-auto mb-3">
              <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke={resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'}
                  strokeWidth="2"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#f59e0b"
                  strokeWidth="2"
                  strokeDasharray="58, 100"
                  className="animate-pulse"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-yellow-500">58%</span>
              </div>
            </div>
            <p className="text-sm font-medium" style={{ color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280' }}>
              Debt Recovery
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
